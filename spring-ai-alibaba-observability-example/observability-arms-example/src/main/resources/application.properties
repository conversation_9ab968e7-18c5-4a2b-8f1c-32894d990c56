spring.application.name=ai
#
#export AI_DASHSCOPE_API_KEY=...
spring.ai.dashscope.api-key=${AI_DASHSCOPE_API_KEY}
#
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.tracing.sampling.probability=1.0
#
spring.threads.virtual.enabled=true

spring.ai.chat.client.observations.include-input=true
spring.ai.chat.observations.include-completion=true
spring.ai.chat.observations.include-prompt=true
spring.ai.image.observations.include-prompt=true
spring.ai.vectorstore.observations.include-query-response=true

#
#spring.docker.compose.lifecycle-management=start_only
