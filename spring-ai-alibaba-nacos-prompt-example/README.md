# Spring AI Alibaba Nacos Prompt Example

演示如何使用 Nacos 的动态配置管理功能管理 Spring AI Alibaba 的 Prompt。达到动态 Prompt 的效果。

1. 启动 Nacos 服务；
2. 写入配置，dataId 为：spring.ai.alibaba.configurable.prompt
3. 在配置中写入：

    ```json
    [
      {
        "name": "author",
        "template": "列出 {author} 有名的著作",
        "model": {
          "key": "余华"
        }
      }
    ]
    ```
   
4. 启动 example 项目，并请求 `http://127.0.0.1:10010/nacos/books` 接口，将会看到如下信息：

```text
鲁迅是中国现代文学史上最重要的作家之一，他的作品对中国文学和社会产生了深远的影响。以下是一些鲁迅最著名的著作：

### 小说集
1. **《呐喊》**  
   - 代表作：《狂人日记》、《阿Q正传》、《孔乙己》、《药》、《故乡》等。
   - 特点：揭示封建社会的黑暗和人民的苦难，呼唤民众觉醒。

2. **《彷徨》**  
   - 代表作：《祝福》、《伤逝》、《离婚》等。
   - 特点：反映知识分子在时代变革中的迷茫与挣扎。

3. **《故事新编》**  
   - 代表作：《补天》、《奔月》、《铸剑》等。
   - 特点：以神话、传说为素材，进行现代化的改编与讽刺。

---

### 散文集
1. **《朝花夕拾》**  
   - 代表作：《从百草园到三味书屋》、《藤野先生》、《范爱农》等。
   - 特点：回忆童年和青年时期的生活经历，充满温情与思考。

---

### 杂文集（部分）
鲁迅的杂文是他思想表达的重要形式，数量众多，以下是部分杂文集：
1. **《坟》**
2. **《热风》**
3. **《华盖集》**
4. **《华盖集续编》**
5. **《而已集》**
6. **《三闲集》**
7. **《二心集》**
8. **《南腔北调集》**
9. **《伪自由书》**
10. **《准风月谈》**
11. **《花边文学》**

---

### 学术著作
1. **《中国小说史略》**  
   - 系统研究中国古代小说发展历史的经典著作。
   
2. **《汉文学史纲要》**  
   - 对中国文学史的梳理与总结。

---

### 翻译作品
鲁迅还翻译了许多外国文学作品，推动了中外文化交流，例如：
- **《域外小说集》**（与周作人合译）
- 翻译果戈理的《死魂灵》等。

鲁迅的作品不仅具有文学价值，更深刻地反映了当时社会的问题，体现了他作为思想家和革命家的责任感。
```

5. 修改 prompt 为：

    ```json
    [
      {
        "name": "author",
        "template": "列出 {author} 有名的著作，只需要书名清单。",
        "model": {
          "key": "鲁迅"
        }
      }
    ]
    ```

6. 在不重启服务的基础上，再次请求 `http://127.0.0.1:10010/nacos/books` 接口，将会看到如下信息：

    ```text
    1. 《呐喊》  
    2. 《彷徨》  
    3. 《野草》  
    4. 《朝花夕拾》  
    5. 《阿Q正传》  
    6. 《坟》  
    7. 《热风》  
    8. 《华盖集》  
    9. 《华盖集续编》  
    10. 《故事新编》  
    11. 《三闲集》  
    12. 《二心集》  
    13. 《南腔北调集》  
    14. 《且介亭杂文》  
    15. 《而已集》
    ```

