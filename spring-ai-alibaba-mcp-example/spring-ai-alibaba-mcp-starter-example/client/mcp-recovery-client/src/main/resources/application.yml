server:
  port: 19100

spring:
  application:
    name: mcp-recovery-client
  ai:
    dashscope:
      api-key: ${DASHSCOPE_API_KEY}
      chat:
        options:
          model: qwen-max
    mcp:
      client:
        enabled: false
        name: my-mcp-client
        version: 1.0.0
        request-timeout: 600s
        type: SYNC  # or ASYNC for reactive applications
        sse:
          connections:
            server1:
              url: http://localhost:19000 # 本地

    alibaba:
      mcp:
        recovery:
          enabled: true
          ping: 5s
          delay: 5s
          stop: 10s


## debug级别
logging:
  level:
    com:
      alibaba:
        cloud:
          ai:
            autoconfigure:
              mcp:
                client: DEBUG
