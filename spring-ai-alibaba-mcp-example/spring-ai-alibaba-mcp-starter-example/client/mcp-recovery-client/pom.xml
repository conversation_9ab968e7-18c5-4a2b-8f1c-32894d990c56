<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.alibaba.cloud.ai</groupId>
        <version>${revision}</version>
        <artifactId>spring-ai-alibaba-mcp-starter-example</artifactId>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>mcp-recovery-client</artifactId>
    <name>Spring AI - MCP Recovery Client EXAMPLE</name>

    <properties>
        <!-- Spring AI Alibaba -->
        <spring-ai-alibaba.version>1.0.0.3-SNAPSHOT</spring-ai-alibaba.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.cloud.ai</groupId>
            <artifactId>spring-ai-alibaba-starter-dashscope</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud.ai</groupId>
            <artifactId>spring-ai-alibaba-starter-mcp-recovery-client</artifactId>
            <version>${spring-ai-alibaba.version}</version>
        </dependency>

    </dependencies>

</project>