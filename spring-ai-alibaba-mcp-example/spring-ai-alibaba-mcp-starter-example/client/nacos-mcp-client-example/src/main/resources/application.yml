server:
  port: 8080

spring:
  application:
    name: mcp-client-webflux
  ai:
    openai:
      api-key: ${AI_DASHSCOPE_API_KEY}
      base-url: https://dashscope.aliyuncs.com/compatible-mode
      chat:
        options:
          model: qwen-max
    alibaba:
      mcp:
        nacos:
          enabled: true
          server-addr: 127.0.0.1:8848
          username: nacos
          password: nacos
          registry:
            service-namespace: 9ba5f1aa-b37d-493b-9057-72918a40ef35
            service-group: mcp-server

        client:
          sse:
            connections:
              server1: mcp-server-provider
    mcp:
      client:
        enabled: true
        name: mcp-client-webflux
        version: 0.0.1
        initialized: true
        request-timeout: 600s

        nacos-enabled: true

        type: sync
        toolcallback:
          enabled: true
        root-change-notification: true


# 调试日志
logging:
  level:
    io:
      modelcontextprotocol:
        client: DEBUG
        spec: DEBUG