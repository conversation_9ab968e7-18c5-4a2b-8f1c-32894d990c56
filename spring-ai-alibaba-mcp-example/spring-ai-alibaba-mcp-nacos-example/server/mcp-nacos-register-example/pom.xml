<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.alibaba.cloud.ai</groupId>
        <artifactId>spring-ai-alibaba-mcp-nacos-example</artifactId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>mcp-nacos-register-example</artifactId>
    <description>Spring AI Alibaba Register MCP Server To Nacos Example</description>
    <name>Spring AI Alibaba Register MCP Server To Nacos Example</name>

    <properties>
        <spring-ai-alibaba.version>*******-SNAPSHOT</spring-ai-alibaba.version>
    </properties>

    <dependencies>

        <!-- Spring Boot Starter Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <!-- MCP Nacos 注册支持 -->
        <dependency>
            <groupId>com.alibaba.cloud.ai</groupId>
            <artifactId>spring-ai-alibaba-starter-nacos-mcp-register</artifactId>
            <version>${spring-ai-alibaba.version}</version>
        </dependency>

        <!-- MCP Server WebMvc 支持（也可换成 WebFlux） -->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-mcp-server-webmvc</artifactId>
        </dependency>

    </dependencies>

</project>
