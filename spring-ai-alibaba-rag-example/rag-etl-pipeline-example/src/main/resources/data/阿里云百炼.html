
<!doctype html>
<html lang="zh">
 <head> 
  <meta name="aplus-auto-track-config-import" content="off"> 
  <meta name="aplus-apm-track" content="off"> 
  <meta name="aplus-core" content="aplus.js"> 
  <meta name="aplus-form-track" content="off"> 
  <meta charset="utf-8"> 
  <meta http-equiv="X-UA-Compatible" content="IE=edge"> 
  <meta name="data-spm" content="5176"> 
  <meta http-equiv="x-dns-prefetch-control" content="on"> 
  <link rel="dns-prefetch" href="//g.alicdn.com"> 
  <link rel="dns-prefetch" href="//uaction.alicdn.com"> 
  <link rel="dns-prefetch" href="//cws.alicdn.com"> 
  <link rel="dns-prefetch" href="//at.alicdn.com"> 
  <link rel="dns-prefetch" href="//gj.mmstat.com"> 
  <link rel="dns-prefetch" href="//ynuf.alipay.com"> 
  <link rel="dns-prefetch" href="//fecs.console.aliyun.com"> 
  <link rel="dns-prefetch" href="//help.aliyun.com"> 
  <link rel="shortcut icon" href="https://img.alicdn.com/tfs/TB1_ZXuNcfpK1RjSZFOXXa6nFXa-32-32.ico" type="image/x-icon"> 
  <title>百炼控制台</title> 
  <script nonce="nHHnTXe8hDZczLzslFm0">
    var ONE_CONSOLE_TOOL = {
        extend: function(o,n){
            if(!o) o = {};
            if(!n) n = {};
            for (var p in n){
                o[p] = n[p];
            }
            return o;
        }
    };
</script> 
  <script src="https://g.alicdn.com/console/performance-reporter/index.js" nonce="nHHnTXe8hDZczLzslFm0"></script> 
  <script async src="https://cloud-assets.alicdn.com/console.js" nonce="nHHnTXe8hDZczLzslFm0"></script> 
  <!--[if lte IE 11]>
<script src="//g.alicdn.com/aliyun/console/1.4.92/scripts/browser-not-supported.js" crossorigin="anonymous"></script>
<![endif]--> 
  <script nonce="nHHnTXe8hDZczLzslFm0">
                var ALIYUN_CONSOLE_CONFIG = {
            APP_ID: "alime_efm",
            PRODUCT: "bailian",
            LANG: "zh",
            LOCALE: "zh-CN",
            portalType: 'one',
                    }
        </script> 
  <script nonce="nHHnTXe8hDZczLzslFm0">
    var ALIYUN_CONSOLE_GLOBAL = {"otherRoutes":[{"menuCode":"app-market","routePath":"/app/app-market/quanmiao","appPath":"iic-llm-solution/bailian-app-quanmiao","appName":"quanmiao-app","version":"1.0.32","loadType":"qiankun"},{"menuCode":"app-market","routePath":"/app/app-market/chip-tool-web","appPath":"liveme-console/chip-tool-web","appName":"chip-tool-web","version":"0.0.3"},{"menuCode":"app-market","appName":"yunmarket","routePath":"/app/app-market/yunmarket","appPath":"market-fe/bailian-app-market-detail","version":"0.0.20"},{"menuCode":"plugin-market","appName":"yunmarket","routePath":"/plugin-market/yunmarket","appPath":"market-fe/bailian-app-market-detail","version":"0.0.20","fileName":"plugin"},{"menuCode":"app-market","routePath":"/app/app-market/ccai","appPath":"isipfe/ccai","appName":"ccai","version":"2.0.11","loadType":"qiankun"},{"menuCode":"app-market","appName":"bailian_beebot","fileName":"bailian_beebot","routePath":"/app/app-market/beebot","appPath":"isipfe/beebot_llm","version":"6.6.2"},{"menuCode":"plugin-market","appName":"ipaas-bailian-plugin","routePath":"/plugin-market/iqs","appPath":"mobi/ipaas-bailian-plugin","version":"0.0.2","loadType":"qiankun"}],"modelSpaceBannerItems":[{"bg":"https://img.alicdn.com/imgextra/i3/O1CN01dg7cUa1tTd1GthToA_!!6000000005903-2-tps-1179-378.png","linkUrl":"https://bailian.console.aliyun.com/#/efm/model_experience_center/vision?currentTab=imageComprehend&modelId=qwen2.5-vl-32b-instruct"},{"bg":"https://img.alicdn.com/imgextra/i3/O1CN01zke9vY1YlOS2IPTv1_!!6000000003099-2-tps-1179-378.png","linkUrl":"https://bailian.console.aliyun.com/#/model-market/detail/qwen2.5-omni-7b?tabKey=sdk"},{"bg":"https://img.alicdn.com/imgextra/i4/O1CN01sqfWCY1ZHSAeZ5BEs_!!6000000003169-2-tps-1176-372.png","linkUrl":"https://www.aliyun.com/activity/purchase/purchasing"}],"bannerItems":[{"title":"qwen2.5-omni-7b开源模型上线，具备文本和语音同时流式生成的能力","linkText":"查看详情","linkUrl":"https://bailian.console.aliyun.com/#/model-market/detail/qwen2.5-omni-7b?tabKey=sdk"},{"title":"aitryon模型价格下调，降至0.2元每张。","linkText":"查看详情","linkUrl":"https://bailian.console.aliyun.com/#/model-market/detail/aitryon"},{"title":"通义千问2.5-VL-32B模型上线，拥有更人性化的回答，更强的数学和图像推理能力。","linkText":"立即体验","linkUrl":"https://bailian.console.aliyun.com/#/efm/model_experience_center/vision?currentTab=imageComprehend&modelId=qwen2.5-vl-32b-instruct"},{"title":"通用文本向量-v3模型新增512/256/128/64等低纬度支持。","linkText":"点击查看","linkUrl":"https://bailian.console.aliyun.com/#/model-market/detail/text-embedding-v3"},{"title":"最强推理模型QwQ-Plus系列上新，核心指标达到DeepSeek-R1 满血版水平。","linkText":"调用方式","linkUrl":"https://bailian.console.aliyun.com/model-market/detail/qwq-plus#/model-market/detail/qwq-plus?tabKey=sdk"},{"title":"全模态Qwen-Omni-Turbo全新上线，支持输入多种模态的数据。","linkText":"查看详情","linkUrl":"https://help.aliyun.com/zh/model-studio/user-guide/qwen-omni"},{"title":"通义千问生态合作伙伴招募计划启动。","linkText":"查看最新伙伴计划","linkUrl":"https://partner.aliyun.com/program/llm_tongyi"}],"appMarketBannerItems":[{"bg":"https://img.alicdn.com/imgextra/i3/O1CN01Afs12m1zEPV5Coh6f_!!6000000006682-2-tps-1176-372.png","linkUrl":"https://survey.aliyun.com/apps/zhiliao/4V74sMCdH"},{"bg":"https://img.alicdn.com/imgextra/i1/O1CN01Em0yn41vpjMETExi4_!!6000000006222-2-tps-1568-496.png","linkUrl":"https://bailian.aliyun.com/quanmiao"},{"bg":"https://img.alicdn.com/imgextra/i4/O1CN019wG5iz1fujmzPSbYL_!!6000000004067-2-tps-1176-372.png","linkUrl":"https://bailian.console.aliyun.com/xiyan#/home"}],"financeFunctionLIst":{"model":{"isShowModelFilter":false,"isShowBanners":false,"isShowCustomModels":false,"isSupportDeploy":false,"isSupportEvaluation":false},"app":{"isShowScheduleSelector":false,"isShowShareConfig":false,"isShowAppFilter":true,"isSupportFlow":true,"isSupportICE":false,"isShowLongTimeMemory":true,"isShowPromptAPI":true,"isSupportAppFlow":false,"isShowInternetSearch":true,"LLMNodeSupportSearch":false,"showSessionFile":false},"rag":{"picSearch":false,"enableRds":true},"dataCenter":{"ossRegionWarning":false,"ADBRegionWarning":false,"supportIMMPreview":false},"plugin":{"supportYunMarketPlugin":false}},"iconFontUrl":"//at.alicdn.com/t/a/font_4647901_trmqjz04j8f.css","listCategories_maxResults":800,"appflowCreateUrl":"https://appflow.console.aliyun.com/vendor/cn-hangzhou/weixin/oauth/modal/connector-3c60c6e123e146fbb6f8/2"};
</script> 
  <script nonce="nHHnTXe8hDZczLzslFm0">
    var ALIYUN_CONSOLE_MESSAGE = {};
    var ALIYUN_CONSOLE_CUSTOM_MESSAGE = {};
</script> 
  <script nonce="nHHnTXe8hDZczLzslFm0">
    var ALIYUN_CONSOLE_I18N_MESSAGE = {};
</script> 
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"> 
  <link rel="icon" type="image/x-icon" href="https://gw.alicdn.com/imgextra/i4/O1CN01vVn7g32134zNZEeAR_!!6000000006928-55-tps-24-24.svg"> 
  <script nonce="nHHnTXe8hDZczLzslFm0">
var urlmap = {
    "https://bailian.console.aliyun.com/#/app/app-market/quanmiao/video-comprehend": "https://bailian.console.aliyun.com/?tab=app#/app/app-market/quanmiao/video-comprehend",
    "https://bailian.console.aliyun.com/#/app/app-market/quanmiao/news-broadcast": "https://bailian.console.aliyun.com/?tab=app#/app/app-market/quanmiao/news-broadcast",
    "https://bailian.console.aliyun.com/?#/home": "https://bailian.console.aliyun.com",
    "https://bailian.console.aliyun.com/#/app/app-market/quanmiao/style-copy": "https://bailian.console.aliyun.com/?tab=app#/app/app-market/quanmiao/style-copy",
    "https://bailian.console.aliyun.com/#/app/app-market/quanmiao/network-content-audit": "https://bailian.console.aliyun.com/?tab=app#/app/app-market/quanmiao/network-content-audit",
    "https://bailian.console.aliyun.com/#/data-center": "https://bailian.console.aliyun.com/?tab=app#/data-center",
    "https://bailian.console.aliyun.com/#/knowledge-base": "https://bailian.console.aliyun.com/?tab=app#/knowledge-base",
    "https://bailian.console.aliyun.com/#/app-center": "https://bailian.console.aliyun.com/?tab=app#/app-center",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/voice?currentTab=voiceTts": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/voice?currentTab=voiceTts",
    "https://bailian.console.aliyun.com/#/prompt-manage": "https://bailian.console.aliyun.com/?tab=app#/plugin-market/prompt",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen-max": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen-max",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen-plus": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen-plus",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen-turbo": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen-turbo",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen-long": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen-long",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen2.5-72b-instruct": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen2.5-72b-instruct",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen2-72b-instruct": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen2-72b-instruct",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen1.5-110b-chat": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen1.5-110b-chat",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen-72b-chat": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen-72b-chat",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center?modelId=farui-plus": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/text?currentTab=textChat&modelId=farui-plus",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/text",
    "https://bailian.console.aliyun.com/#/efm/model_evaluate": "https://bailian.console.aliyun.com/?tab=model#/efm/model_evaluate",
    "https://bailian.console.aliyun.com/#/model-telemetry": "https://bailian.console.aliyun.com/?tab=model#/model-telemetry",
    "https://bailian.console.aliyun.com/#/home": "https://bailian.console.aliyun.com",
    "https://bailian.console.aliyun.com/?apiKey=1#/api-key": "https://bailian.console.aliyun.com/?tab=model#/api-key",
    "https://bailian.console.aliyun.com/?#/model-market": "https://bailian.console.aliyun.com/?tab=model#/model-market",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/text": "https://bailian.console.aliyun.com/console?tab=model#/efm/model_experience_center/text",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen-max": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen-max",
    "https://bailian.console.aliyun.com/?#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen-plus": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen-plus",
    "https://bailian.console.aliyun.com/?#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen-turbo": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen-turbo",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen-vl-max": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen-max",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen-vl-ocr": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen-vl-ocr",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen-math-plus": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen-math-plus",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen-coder-turbo": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen-coder-turbo",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/text?currentTab=textChat&modelId=qwq-32b-preview": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/text?currentTab=textChat&modelId=qwq-32b-preview",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen2.5-math-72b-instruct": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen2.5-math-72b-instruct",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen2.5-coder-7b-instruct": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen2.5-coder-7b-instruct",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen-plus-0112": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen-plus",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/vision": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/vision",
    "https://bailian.console.aliyun.com/#/plugin-market": "https://bailian.console.aliyun.com/?tab=app#/plugin-market/plugin",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/vision?currentTab=imageComprehend&modelId=qwen-vl-ocr": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/vision?currentTab=imageComprehend&modelId=qwen-vl-ocr",
    "https://bailian.console.aliyun.com/#/custom-plugins": "https://bailian.console.aliyun.com/?tab=app#/component-manage/plugin",
    "https://bailian.console.aliyun.com/#/efm/model_evaluate/task/create": "https://bailian.console.aliyun.com/?tab=model#/efm/model_evaluate/task/create",
    "https://bailian.console.aliyun.com/model_experience_center#/efm/model_experience_center/text?currentTab=textDebug": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/text?currentTab=textDebug",
    "https://bailian.console.aliyun.com/#/efm/model_manager": "https://bailian.console.aliyun.com/?tab=model#/efm/model_manager",
    "https://bailian.console.aliyun.com/#/efm/model_deploy": "https://bailian.console.aliyun.com/?tab=model#/efm/model_deploy",
    "https://bailian.console.aliyun.com/#/data-process": "https://bailian.console.aliyun.com/?tab=model#/efm/model_data",
    "https://bailian.console.aliyun.com/#/efm/model_data": "https://bailian.console.aliyun.com/?tab=model#/efm/model_data",
    "https://bailian.console.aliyun.com/#/efm/model_data/createDataAss?isApplicationTabContent=false": "https://bailian.console.aliyun.com/?tab=model#/efm/model_data/createDataAss?isApplicationTabContent=false",
    "https://bailian.console.aliyun.com/#/efm/model_deploy/create": "https://bailian.console.aliyun.com/?tab=model#/efm/model_deploy/create",
    "https://bailian.console.aliyun.com/knowledge-base#/data-process": "https://bailian.console.aliyun.com/?tab=model#/efm/model_data",
    "https://bailian.console.aliyun.com/knowledge-base#/efm/model_data": "https://bailian.console.aliyun.com/?tab=model#/efm/model_data",
    "https://bailian.console.aliyun.com/#/efm/dimension_template": "https://bailian.console.aliyun.com/?tab=model#/efm/model_evaluate/dimension_template",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen-max-2025-01-25": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen-max",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/text?currentTab=textChat&modelId=deepseek-r1": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/text?currentTab=textChat&modelId=deepseek-r1",
    "https://bailian.console.aliyun.com/#/model-market/detail/deepseek-r1": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/deepseek-r1",
    "https://bailian.console.aliyun.com/#/model-market/detail/deepseek-r1?tabKey=sdk": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/deepseek-r1?tabKey=sdk",
    "https://bailian.console.aliyun.com/#/model-market/detail/deepseek-v3": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/deepseek-v3",
    "https://bailian.console.aliyun.com/#/model-market/detail/motionshop-video-detect": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/motionshop-video-detect",
    "https://bailian.console.aliyun.com/#/model-market/detail/motionshop-gen3d": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/motionshop-gen3d",
    "https://bailian.console.aliyun.com/#/model-market/detail/motionshop-synthesis": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/motionshop-synthesis",
    "https://bailian.console.aliyun.com/#/data-center/category-create?dataType=1": "https://bailian.console.aliyun.com/?tab=app#/data-center/category-create?dataType=1",
    "https://bailian.console.aliyun.com/#/knowledge-base/create": "https://bailian.console.aliyun.com/?tab=app#/knowledge-base/create",
    "https://bailian.console.aliyun.com/knowledge-base#/home": "https://bailian.console.aliyun.com",
    "https://bailian.console.aliyun.com/knowledge-base#/data-center": "https://bailian.console.aliyun.com/?tab=app#/data-center",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/text?currentTab=textChat&modelId=qwq-plus": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/text?currentTab=textChat&modelId=qwq-plus",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwq-plus": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwq-plus",
    "https://bailian.console.aliyun.com/#/authority/member": "https://bailian.console.aliyun.com/?tab=model#/authority",
    "https://bailian.console.aliyun.com/#/model-market/detail/facechain-generation": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/facechain-generation",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/voice": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/voice",
    "https://bailian.console.aliyun.com/#/app-market": "https://bailian.console.aliyun.com/?tab=app#/app-market",
    "https://bailian.console.aliyun.com/knowledge-base#/knowledge-base": "https://bailian.console.aliyun.com/?tab=app#/knowledge-base",
    "https://bailian.console.aliyun.com/#/app-center/": "https://bailian.console.aliyun.com/?tab=app#/app-center",
    "https://bailian.console.aliyun.com/#/app/app-market/beebot": "https://bailian.console.aliyun.com/?tab=app#/app/app-market/beebot",
    "https://bailian.console.aliyun.com/#/app/app-market/quanmiao/market-write": "https://bailian.console.aliyun.com/?tab=app#/app/app-market/quanmiao/market-write",
    "https://bailian.console.aliyun.com/#/app/app-market/quanmiao/script-create": "https://bailian.console.aliyun.com/?tab=app#/app/app-market/quanmiao/script-create",
    "https://bailian.console.aliyun.com/#/app/app-market/quanmiao/voc": "https://bailian.console.aliyun.com/?tab=app#/app/app-market/quanmiao/voc",
    "https://bailian.console.aliyun.com/#/model-market/detail/llama3.1-405b-instruct": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/llama3.1-405b-instruct",
    "https://bailian.console.aliyun.com/#/model-market/detail/llama3.2-11b-vision": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/llama3.2-11b-vision",
    "https://bailian.console.aliyun.com/#/model-market/detail/baichuan2-turbo": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/baichuan2-turbo",
    "https://bailian.console.aliyun.com/#/model-market/detail/baichuan2-13b-chat-v1": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/baichuan2-13b-chat-v1",
    "https://bailian.console.aliyun.com/#/model-market/detail/chatglm3-6b": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/chatglm3-6b",
    "https://bailian.console.aliyun.com/#/model-market/detail/yi-large": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/yi-large",
    "https://bailian.console.aliyun.com/#/model-market/detail/abab6.5s-chat": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/abab6.5s-chat",
    "https://bailian.console.aliyun.com/#/model-market/detail/wanx-v1": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/wanx-v1",
    "https://bailian.console.aliyun.com/#/model-market/detail/flux-merged": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/flux-merged",
    "https://bailian.console.aliyun.com/#/model-market/detail/cosyvoice-v1": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/cosyvoice-v1",
    "https://bailian.console.aliyun.com/#/model-market/detail/paraformer-v2": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/paraformer-v2",
    "https://bailian.console.aliyun.com/#/model-market/detail/paraformer-realtime-v2": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/paraformer-realtime-v2",
    "https://bailian.console.aliyun.com/#/model-market/detail/farui-plus": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/farui-plus",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center?modelId=qwen-long": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/text?modelId=qwen-long",
    "https://bailian.console.aliyun.com/#/app/app-market/quanmiao/enterprise-clue-mining": "https://bailian.console.aliyun.com/?tab=app#/app/app-market/quanmiao/enterprise-clue-mining",
    "https://bailian.console.aliyun.com/home#/efm/model_deploy": "https://bailian.console.aliyun.com/tab=model?tab=model#/efm/model_deploy",
    "https://bailian.console.aliyun.com/knowledge-base#/app-observe": "https://bailian.console.aliyun.com/?tab=app#/app-observe",
    "https://bailian.console.aliyun.com/#/efm/model_manager/create": "https://bailian.console.aliyun.com/?tab=model#/efm/model_manager/create",
    "https://bailian.console.aliyun.com/#/data-process/create": "https://bailian.console.aliyun.com/?tab=model#/efm/model_data",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen-max-latest": "https://bailian.console.aliyun.com/?tab=model#/model-market/detail/qwen-max",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen2.5-omni-7b": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/qwen2.5-omni-7b",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/vision?currentTab=imageComprehend&modelId=qvq-max": "https://bailian.console.aliyun.com/console?tab=model#/efm/model_experience_center/vision",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/vision?currentTab=imageComprehend&modelId=qwen2.5-vl-32b-instruct": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/qwen2.5-vl-32b-instruct",
    "https://bailian.console.aliyun.com/#/model-market": "https://bailian.console.aliyun.com/console?tab=model#/model-market",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen-plus": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen-plus",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen-turbo": "https://bailian.console.aliyun.com/?tab=model#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen-turbo",
    "https://bailian.console.aliyun.com/#/model-market/detail/qwen-omni-turbo": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/qwen-omni-turbo",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/vision?currentTab=imageComprehend&modelId=qwen-vl-max": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/qwen-vl-max",
    "https://bailian.console.aliyun.com/#/model-market/detail/wanx2.1-i2v-plus": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/wanx2.1-i2v-plus",
    "https://bailian.console.aliyun.com/#/model-market/detail/wanx2.1-t2v-plus": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/wanx2.1-t2v-plus",
    "https://bailian.console.aliyun.com/#/model-market/detail/wanx2.1-t2i-plus": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/wanx2.1-t2i-plus",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/vision?currentTab=imageGenerate&modelId=aitryon": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/aitryon",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen-coder-plus": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/qwen-coder-plus",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/text?currentTab=textChat&modelId=qwq-32b": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/qwq-32b",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/vision?currentTab=imageComprehend&modelId=qvq-72b-preview": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/qvq-72b-preview",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/vision?currentTab=imageComprehend&modelId=qwen2.5-vl-72b-instruct": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/qwen2.5-vl-72b-instruct",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen2.5-72b-instruct": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/qwen2.5-72b-instruct",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen2.5-14b-instruct-1m": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/qwen2.5-14b-instruct-1m",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen2.5-math-72b-instruct": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/qwen2.5-math-72b-instruct",
    "https://bailian.console.aliyun.com/#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen2.5-coder-32b-instruct": "https://bailian.console.aliyun.com/console?tab=model#/model-market/detail/qwen2.5-coder-32b-instruct",
    "https://bailian.console.aliyun.com/xiyan?switchAgent=10039957&productCode=p_efm#/dataManagement/dataSourceM": "https://bailian.console.aliyun.com/xiyan#/dataManagement/dataSourceM",
    "https://bailian.console.aliyun.com/xiyan?switchAgent=10039957&productCode=p_efm#/systemSettings/authority":"https://bailian.console.aliyun.com/xiyan#/systemSettings/authority",
    "https://bailian.console.aliyun.com/xiyan?switchAgent=10039957&productCode=p_efm#/dataManagement/dataSourceM":"https://bailian.console.aliyun.com/xiyan#/dataManagement/dataSourceM",
    "https://bailian.console.aliyun.com/?globalset=1#/efm/global_set":"https://bailian.console.aliyun.com/?tab=globalset#/efm/global_set",
    "https://bailian.console.aliyun.com/?admin=1#/efm/business_management":"https://bailian.console.aliyun.com/?tab=globalset#/efm/business_management",
}


var currentUrl = new URL(location.href);
currentUrl.searchParams.delete('spm');
currentUrl.searchParams.delete('accounttraceid');

var newUrl = urlmap[currentUrl.toString()];
if (newUrl) {
  location.replace(newUrl);
}
</script> 
  <script nonce="nHHnTXe8hDZczLzslFm0">
window.CONSOLE_BASE_SETTINGS = {
  TOP_NAV: {
    I18N: false
  },
  REGION: {
    regions: [] // isipfe/base中修改regions
  },
  regionIdDefault: 'cn-shanghai'
};
    document.title = '阿里云百炼';
 document.domain = 'console.aliyun.com'
  
  // 如果访问百炼对内, 跳转到新的百炼对内页面
  if (location.search.includes('productCode=p_llm_platform')) {
    location.href = 'https://qwen-dev.console.aliyun.com/';
  }
</script> 
  <script nonce="nHHnTXe8hDZczLzslFm0">
    window.XT = function(){}
    window.g_apps = window.ALIYUN_CONSOLE_GLOBAL.g_apps || {};
    window.g_cardview = {
      cdn: '//g.alicdn.com'
    };
    window.g_config = {
      // 云小蜜配置项
      env: 'online',
      productMode: 'Cloudme',
      AliyunRegion: 'cn-beijing',
      AliyunProduct: 'sfm_bailian',
      aemId: 'bailian',
      ramRegion: 'cn-beijing', // ram请求时传的region值
      ossRegion: 'cn-beijing', // '我的模型'中oss支持的region值
    };
    window.g_open_config = {
      base: {
        buyAgentUrl: 'https://common-buy.aliyun.com/?commodityCode=sfm_platform_public_cn',
        sseHost: 'efm-ws.aliyuncs.com'
      }
    };
    
    Object.assign(g_apps, {
      // 基础组件
      'isipfe/beebot': {
        version: '6.2.3',
        "globalDeps": [
           'https://g.alicdn.com/isipfe/beebot/6.2.3/lib/dayjs/dayjs.min.js',
           'https://g.alicdn.com/isipfe/beebot/6.2.3/lib/antd5/antd5.min.js',
          'https://g.alicdn.com/isipfe/beebot/6.2.3/xiaomi-common/xiaomi-common.css',
           'https://g.alicdn.com/isipfe/beebot/6.2.3/xiaomi-common/xiaomi-common.js',
        ]
      },
      'isipfe/template': {version: '6.0.0'},
      'isipfe/efm-fe': {version: '3.2.65', loadType: 'fall'},
    });
  
    window.g_base = {_loaded: true};
</script> 
  <script nonce="nHHnTXe8hDZczLzslFm0">
    window.tenantId = window.ALIYUN_CONSOLE_CONFIG.CURRENT_PK;
    // 国内站，强制语言是中文
    window.ALIYUN_CONSOLE_CONFIG.LOCALE = 'zh-CN';
</script> 
  <script src="https://g.alicdn.com/IMM/office-js/1.1.19/aliyun-web-office-sdk.min.js" nonce="nHHnTXe8hDZczLzslFm0"></script> 
  <!--加载百炼css--> 
  <link rel="stylesheet" href="//g.alicdn.com/isipfe/efm-fe/3.2.65/efm.css"> 
  <script src="//g.alicdn.com/isipfe/beebot/6.2.3/lib/react/18.2.0/react.production.min.js" nonce="nHHnTXe8hDZczLzslFm0"></script> 
  <script src="//g.alicdn.com/isipfe/beebot/6.2.3/lib/react/18.2.0/react-dom.production.min.js" nonce="nHHnTXe8hDZczLzslFm0"></script>
  <script src="//g.alicdn.com/code/npm/@ali/g_aem_track/1.1.2/bailian.js" nonce="nHHnTXe8hDZczLzslFm0"></script> 
 </head> 
 <body data-spm="28197581"> 
  <script nonce="nHHnTXe8hDZczLzslFm0">
    window.APLUS_CONFIG = {
        pid: 'non_cloud_console_bailian',
        env: 'production',
                userId: window.ALIYUN_CONSOLE_CONFIG.CURRENT_PK || '',
        pvTrack: window._pvTrackOpt || 'off',
        jsErrorTrack: window._jsErrorTrackOpt || {},
        apiTrack: window._apiTrackOpt || { sampling: 1 },
        perfTrack: window._perfTrackOpt || {},
        resourceErrorTrack: window._resourceErrorTrackOpt || {},
        blankScreenTrack: window._blankScreenTrackOpt || {}
    };

    (function(w, d, s, q, i) {
        w[q] = w[q] || [];
        var f = d.getElementsByTagName(s)[0],j = d.createElement(s);
        j.async = true;
        j.id = 'beacon-aplus';
        j.setAttribute('exparams','userid='+(window.ALIYUN_CONSOLE_CONFIG.CURRENT_PK || '')+'&aplus&sidx=aplusSidex&ckx=aplusCkx&yunid=&yunpk=&c2=alime_efm'+'&current_pk='+(window.ALIYUN_CONSOLE_CONFIG.CURRENT_PK || ''));
        j.src = "//g.alicdn.com/alilog/mlog/aplus_v2.js";
        j.crossorigin = 'anonymous';
        f.parentNode.insertBefore(j, f);
    })(window, document, 'script', 'aplus_queue');
</script> 
  <div id="AliyunConsoleOverlay" style="position: fixed;top:0;left:0;width:100%;height:100%;background: #F5F6FA;color: #333;text-align: center;"> 
   <div style="position:relative;top:38.2%;margin-top:-7px;font:italic 14px/1em Helvetica Neue,Hiragino Sans GB,STHeiti,Microsoft YaHei;">
     Loading... 
   </div> 
  </div> 
  <script src="//cws.alicdn.com/one-mcms/visage/1.0.5/visage_zh-cn.js" charset="utf-8" nonce="nHHnTXe8hDZczLzslFm0"></script> 
  <script nonce="nHHnTXe8hDZczLzslFm0">
    var aliyunConsoleI18nMessage = {};
    aliyunConsoleI18nMessage = ONE_CONSOLE_TOOL.extend(window["visage_zh-cn"], aliyunConsoleI18nMessage);
</script> 
  <script nonce="nHHnTXe8hDZczLzslFm0">
    var ALIYUN_VISAGE_MESSAGE = aliyunConsoleI18nMessage;
</script> 
  <script nonce="nHHnTXe8hDZczLzslFm0">
  var aliyunConsoleOverlay = document.getElementById('AliyunConsoleOverlay');
  aliyunConsoleOverlay.innerHTML = '<img style="position:absolute;left:50%;top:50%;transform: translate(-50%, -50%)" width="100" height="100" src="https://img.alicdn.com/imgextra/i4/O1CN01QNK14B1ERtH3A7DUk_!!6000000000349-54-tps-512-512.apng" />'
  
</script> 
  <script crossorigin src="//g.alicdn.com/??/AWSC/AWSC/awsc.js,/sd/baxia-entry/baxiaCommon.js" nonce="nHHnTXe8hDZczLzslFm0"></script> 
  <div id="root"></div> 
  <script nonce="nHHnTXe8hDZczLzslFm0">
    window.FastLoginContext = {
      tenantName: 'website',
    };
</script> 
  <script src="//g.alicdn.com/dawn/assets-loader/scripts/fast-login.js" nonce="nHHnTXe8hDZczLzslFm0"></script> 
  <script crossorigin src="//g.alicdn.com/isipfe/beebot/6.2.3/lib/qiankun/qiankun.min.js" nonce="nHHnTXe8hDZczLzslFm0"></script> 
  <!--加载百炼--> 
  <script crossorigin src="//g.alicdn.com/isipfe/efm-fe/3.2.65/efm.js" nonce="nHHnTXe8hDZczLzslFm0"></script> 
  <!-- @Deprecated --> 
  <script src="//o.alicdn.com/console/hijack-for-service/index.js" async nonce="nHHnTXe8hDZczLzslFm0"></script>  
 </body>
</html>