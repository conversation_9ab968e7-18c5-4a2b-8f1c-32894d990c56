package com.alibaba.cloud.ai.application.entity.mcp;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import org.springframework.ai.mcp.client.autoconfigure.properties.McpStdioClientProperties;

/**
 * <AUTHOR>
 * <AUTHOR> href="mailto:<EMAIL>">yuluo</a>
 */

public class McpServerConfig {

	@JsonProperty("mcpServers")
	private Map<String, McpStdioClientProperties.Parameters> mcpServers;

	public Map<String, McpStdioClientProperties.Parameters> getMcpServers() {
		return mcpServers;
	}

	public void setMcpServers(Map<String, McpStdioClientProperties.Parameters> mcpServers) {
		this.mcpServers = mcpServers;
	}

}
