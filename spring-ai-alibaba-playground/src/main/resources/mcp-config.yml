mcpServers:
  my-weather-server:
    command: java
    args:
      - "-Dspring.ai.mcp.server.stdio=true"
      - "-Dspring.main.web-application-type=none"
      - "-Dlogging.pattern.console="
      - "-jar"
      - "mcp-libs/weather.jar"
    env: {}
  github-mcp-server:
    # windows 下为 npx.cmd mac 为 npx
    command: npx
    args:
      - "-y"
      - "@modelcontextprotocol/server-github"
    env:
      GITHUB_PERSONAL_ACCESS_TOKEN: ""
