#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

spring:
  ai:
    dashscope:
      api-key: ${AI_DASHSCOPE_API_KEY}
    # 自定义增强 mcp server config
    # mcp:
    #   client:
    #     stdio:
    #       servers-configuration: classpath:/mcp-servers-config.json

    mcp:
      client:
        toolcallback:
          enabled: true
    alibaba:
      tool-calling:
        baidu:
          translate:
            # 显式开启 Tool Bean 注入
            enabled: true
            app-id: ${BAIDU_TRANSLATE_APP_ID}
            secret-key: ${BAIDU_TRANSLATE_SECRET_KEY}
          map:
            enabled: true
            api-key: ${BAIDU_MAP_API_KEY}

    # openai api 接入通义模型
    openai:
      base-url: https://dashscope.aliyuncs.com/compatible-mode
      api-key: ${AI_DASHSCOPE_API_KEY}
      chat:
        options:
          model: qwen-max-latest

    # 注意：观测数据可能存在敏感信息！
    chat:
      client:
        observations:
          # 记录调用输入
          include-input: false
      observations:
        # 记录 LLMs 输出
        include-completion: false
        # 记录 prompt
        include-prompt: false

  datasource:
    url: *****************************************
    driver-class-name: org.sqlite.JDBC

  jpa:
    show-sql: false
    database-platform: org.hibernate.community.dialect.SQLiteDialect

    hibernate:
      ddl-auto: create-drop

    properties:
      hibernate:
        boot:
          allow_jdbc_metadata_access: false
  iqs:
    search:
      api-key: ${IQS_SEARCH_API_KEY}
