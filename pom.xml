<?xml version="1.0" encoding="UTF-8"?>

<!--
   Copyright 2024-2026 the original author or authors.

   Licensed under the Apache License, Version 2.0 (the "License");
   you may not use this file except in compliance with the License.
   You may obtain a copy of the License at

        https://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
-->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>
	<groupId>com.alibaba.cloud.ai</groupId>
	<artifactId>spring-ai-alibaba-examples</artifactId>
	<version>${revision}</version>
	<packaging>pom</packaging>

	<description>Spring AI Alibaba Examples</description>
	<name>Spring AI Alibaba Examples</name>
	<url>https://github.com/springaialibaba/spring-ai-alibaba-examples</url>

	<properties>
		<!-- Project revision -->
		<revision>1.0.0</revision>

		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>17</java.version>
		<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>

		<!-- Spring AI -->
		<spring-ai.version>1.0.0</spring-ai.version>

		<!-- Spring AI Alibaba -->
		<spring-ai-alibaba.version>*******</spring-ai-alibaba.version>

		<!-- Spring Boot -->
		<spring-boot.version>3.4.0</spring-boot.version>

		<!-- maven plugin -->
		<maven-deploy-plugin.version>3.1.1</maven-deploy-plugin.version>
		<flatten-maven-plugin.version>1.3.0</flatten-maven-plugin.version>
		<maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
	</properties>

	<modules>
		<module>spring-ai-alibaba-playground</module>
		<module>spring-ai-alibaba-helloworld</module>
		<module>spring-ai-alibaba-mcp-example</module>
		<module>spring-ai-alibaba-rag-example</module>
		<module>spring-ai-alibaba-chat-example</module>
		<module>spring-ai-alibaba-audio-example</module>
		<module>spring-ai-alibaba-agent-example</module>
		<module>spring-ai-alibaba-graph-example</module>
		<module>spring-ai-alibaba-image-example</module>
		<module>spring-ai-alibaba-prompt-example</module>
		<module>spring-ai-alibaba-usecase-example</module>
		<module>spring-ai-alibaba-structured-example</module>
		<module>spring-ai-alibaba-multi-model-example</module>
		<module>spring-ai-alibaba-chat-memory-example</module>
		<module>spring-ai-alibaba-tool-calling-example</module>
		<module>spring-ai-alibaba-nacos-prompt-example</module>
		<module>spring-ai-alibaba-observability-example</module>
		<module>spring-ai-alibaba-more-platform-and-model-example</module>
	</modules>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${spring-boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.springframework.ai</groupId>
				<artifactId>spring-ai-bom</artifactId>
				<version>${spring-ai.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.alibaba.cloud.ai</groupId>
				<artifactId>spring-ai-alibaba-bom</artifactId>
				<version>${spring-ai-alibaba.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring-boot.version}</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-deploy-plugin</artifactId>
				<version>${maven-deploy-plugin.version}</version>
				<configuration>
					<skip>true</skip>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>${maven-compiler-plugin.version}</version>
				<configuration>
					<release>${java.version}</release>
					<compilerArgs>
						<compilerArg>-parameters</compilerArg>
					</compilerArgs>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>flatten-maven-plugin</artifactId>
				<version>${flatten-maven-plugin.version}</version>
				<inherited>true</inherited>
				<executions>
					<execution>
						<id>flatten</id>
						<phase>process-resources</phase>
						<goals>
							<goal>flatten</goal>
						</goals>
						<configuration>
							<updatePomFile>true</updatePomFile>
							<flattenMode>ossrh</flattenMode>
							<pomElements>
								<distributionManagement>remove</distributionManagement>
								<dependencyManagement>remove</dependencyManagement>
								<repositories>remove</repositories>
								<scm>keep</scm>
								<url>keep</url>
								<organization>resolve</organization>
							</pomElements>
						</configuration>
					</execution>
					<execution>
						<id>flatten.clean</id>
						<phase>clean</phase>
						<goals>
							<goal>clean</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

	<repositories>
		<repository>
			<id>spring-milestones</id>
			<name>Spring Milestones</name>
			<url>https://repo.spring.io/milestone</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>spring-snapshots</id>
			<name>Spring Snapshots</name>
			<url>https://repo.spring.io/snapshot</url>
			<releases>
				<enabled>false</enabled>
			</releases>
		</repository>
		<repository>
			<id>sonatype</id>
			<name>OSS Sonatype</name>
			<url>https://oss.sonatype.org/content/groups/public/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>aliyunmaven</id>
			<name>aliyun</name>
			<url>https://maven.aliyun.com/repository/public</url>
		</repository>
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>public</id>
			<name>aliyun nexus</name>
			<url>https://maven.aliyun.com/repository/public</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</pluginRepository>
	</pluginRepositories>

</project>
